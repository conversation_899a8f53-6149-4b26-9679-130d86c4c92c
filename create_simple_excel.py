#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
KobiPanel CRUD İşlemleri Excel Dosyası Oluşturucu (Basit Versiyon)
Bu script, CSV dosyalarını Excel formatına dönüştürür.
"""

import csv
import os
from datetime import datetime

def create_excel_with_csv_data():
    """CSV verilerini kullanarak Excel benzeri yapı oluşturur"""
    
    # Tüm sayfalar için veri yapısı
    sheets_data = {
        "1-MÜŞTERİLER": {
            "headers": ["İŞLEM TİPİ", "ID", "AD SOYAD", "TELEFON", "YAŞ", "<PERSON>L", "İLÇE", "MAHALLE", "KÜÇÜKBAŞ HAYVAN", "BÜYÜKBAŞ HAYVAN", "KAYIT TARİHİ", "AÇIKLAMA"],
            "examples": [
                ["EKLE", "", "Ahmet Yılmaz", "05551234567", "45", "İstanbul", "Kadıköy", "Fenerbahçe", "50", "20", "2024-01-15", "Yeni müşteri"],
                ["GÜNCELLE", "1", "Mehmet Demir", "05559876543", "38", "Ankara", "Çankaya", "Kızılay", "30", "15", "", "Telefon güncellendi"],
                ["SİL", "2", "", "", "", "", "", "", "", "", "", "Müşteriyi sil"],
                ["", "", "", "", "", "", "", "", "", "", "", ""],
                ["AÇIKLAMA:", "", "", "", "", "", "", "", "", "", "", ""],
                ["", "EKLE: Yeni müşteri eklemek için", "", "", "", "", "", "", "", "", "", ""],
                ["", "GÜNCELLE: Mevcut müşteriyi güncellemek için ID gerekli", "", "", "", "", "", "", "", "", "", ""],
                ["", "SİL: Müşteriyi silmek için sadece ID yeterli", "", "", "", "", "", "", "", "", "", ""],
                ["", "ID: Güncelleme ve silme işlemleri için gerekli", "", "", "", "", "", "", "", "", "", ""],
                ["", "Tarih formatı: YYYY-MM-DD (örn: 2024-01-15)", "", "", "", "", "", "", "", "", "", ""]
            ]
        },
        "2-ÜRÜNLER": {
            "headers": ["İŞLEM TİPİ", "ÜRÜN ID", "ÜRÜN ADI", "FİYAT (TL)", "STOK", "NAKLİYE ÜCRETİ", "EKLENME TARİHİ", "AÇIKLAMA"],
            "examples": [
                ["EKLE", "", "Buğday", "15.50", "1000", "2.00", "2024-01-15", "Kaliteli buğday"],
                ["GÜNCELLE", "1", "Arpa", "12.75", "500", "1.50", "", "Fiyat güncellendi"],
                ["SİL", "2", "", "", "", "", "", "Ürünü sil"],
                ["", "", "", "", "", "", "", ""],
                ["AÇIKLAMA:", "", "", "", "", "", "", ""],
                ["", "Fiyatlar virgülle yazılabilir (örn: 15,50)", "", "", "", "", "", ""],
                ["", "Stok sayısal değer olmalı", "", "", "", "", "", ""],
                ["", "Nakliye ücreti opsiyonel", "", "", "", "", "", ""]
            ]
        },
        "3-SATIŞLAR": {
            "headers": ["İŞLEM TİPİ", "SATIŞ ID", "MÜŞTERİ ID", "ÜRÜN ID", "ADET", "TUTAR", "İNDİRİM", "SERVİS", "KREDİ KARTI", "NAKLİYE", "TESLİM DURUMU", "SATIŞ TARİHİ", "AÇIKLAMA"],
            "examples": [
                ["EKLE", "", "1", "1", "100", "1550.00", "50.00", "EVET", "HAYIR", "200", "HAYIR", "2024-01-15", "Toplu satış"],
                ["GÜNCELLE", "1", "", "", "", "", "", "", "", "", "EVET", "", "Teslim edildi"],
                ["SİL", "2", "", "", "", "", "", "", "", "", "", "", "Satışı iptal et"],
                ["", "", "", "", "", "", "", "", "", "", "", "", ""],
                ["AÇIKLAMA:", "", "", "", "", "", "", "", "", "", "", "", ""],
                ["", "Servis: EVET/HAYIR (Adrese teslim mi?)", "", "", "", "", "", "", "", "", "", "", ""],
                ["", "Kredi Kartı: EVET/HAYIR", "", "", "", "", "", "", "", "", "", "", ""],
                ["", "Teslim Durumu: EVET/HAYIR", "", "", "", "", "", "", "", "", "", "", ""],
                ["", "Müşteri ID ve Ürün ID'leri diğer sayfalardan alın", "", "", "", "", "", "", "", "", "", "", ""]
            ]
        },
        "4-GİDERLER": {
            "headers": ["İŞLEM TİPİ", "GİDER ID", "KONU", "TUTAR (TL)", "TARİH"],
            "examples": [
                ["EKLE", "", "Yakıt", "500.00", "2024-01-15"],
                ["EKLE", "", "Elektrik Faturası", "250.75", "2024-01-15"],
                ["GÜNCELLE", "1", "Yakıt (Güncellenmiş)", "550.00", ""],
                ["SİL", "2", "", "", ""],
                ["", "", "", "", ""],
                ["AÇIKLAMA:", "", "", "", ""],
                ["", "Konu: Giderin ne olduğunu açıklayın", "", "", ""],
                ["", "Tutar: Virgülle yazabilirsiniz (örn: 250,75)", "", "", ""],
                ["", "Tarih: YYYY-MM-DD formatında", "", "", ""]
            ]
        },
        "5-NOTLAR": {
            "headers": ["İŞLEM TİPİ", "NOT ID", "KONU", "AÇIKLAMA", "TARİH"],
            "examples": [
                ["EKLE", "", "Toplantı", "Yarın saat 14:00'te müşteri toplantısı", "2024-01-15"],
                ["EKLE", "", "Hatırlatma", "Ay sonunda faturaları kontrol et", "2024-01-15"],
                ["GÜNCELLE", "1", "Toplantı İptal", "Toplantı ertelendi", ""],
                ["SİL", "2", "", "", ""],
                ["", "", "", "", ""],
                ["AÇIKLAMA:", "", "", "", ""],
                ["", "Konu: Notun başlığı", "", "", ""],
                ["", "Açıklama: Detaylı bilgi", "", "", ""],
                ["", "Tarih otomatik eklenir", "", "", ""]
            ]
        },
        "6-ARAÇLAR": {
            "headers": ["İŞLEM TİPİ", "ARAÇ ID", "PLAKA", "MARKA", "MODEL", "MODEL YILI", "VİZE BİTİŞ", "SİGORTA BİTİŞ", "EGZOZ BİTİŞ", "TESCİL NO", "AÇIKLAMA"],
            "examples": [
                ["EKLE", "", "34ABC123", "Ford", "Transit", "2020", "2024-12-31", "2024-06-30", "2024-03-15", "12345678", "İş aracı"],
                ["GÜNCELLE", "1", "", "", "", "", "2025-12-31", "", "", "", "Vize yenilendi"],
                ["SİL", "2", "", "", "", "", "", "", "", "", ""],
                ["", "", "", "", "", "", "", "", "", "", ""],
                ["AÇIKLAMA:", "", "", "", "", "", "", "", "", "", ""],
                ["", "Plaka: 34ABC123 formatında", "", "", "", "", "", "", "", "", ""],
                ["", "Tarihler: YYYY-MM-DD formatında", "", "", "", "", "", "", "", "", ""],
                ["", "Model yılı: Sadece yıl (örn: 2020)", "", "", "", "", "", "", "", "", ""]
            ]
        },
        "7-YAKIT TAKİBİ": {
            "headers": ["İŞLEM TİPİ", "YAKIT ID", "ARAÇ ID", "TUTAR (TL)", "LİTRE", "ALINDIĞI YER", "KM", "TARİH", "LİTRE FİYATI", "AÇIKLAMA"],
            "examples": [
                ["EKLE", "", "1", "500.00", "25.5", "Shell Benzinlik", "125000", "2024-01-15", "19.60", "Tam depo"],
                ["GÜNCELLE", "1", "", "520.00", "", "", "", "", "", "Tutar düzeltildi"],
                ["SİL", "2", "", "", "", "", "", "", "", ""],
                ["", "", "", "", "", "", "", "", "", ""],
                ["AÇIKLAMA:", "", "", "", "", "", "", "", "", ""],
                ["", "Araç ID: Araçlar sayfasından alın", "", "", "", "", "", "", "", ""],
                ["", "Litre fiyatı otomatik hesaplanabilir", "", "", "", "", "", "", "", ""],
                ["", "KM: Yakıt alındığı andaki kilometre", "", "", "", "", "", "", "", ""]
            ]
        },
        "8-TARLA YÖNETİMİ": {
            "headers": ["İŞLEM TİPİ", "TARLA ID", "MÜŞTERİ ID", "TARLA ADI", "DÖNÜM", "BİÇME FİYATI", "TOPLAM TUTAR", "TAHSİLAT", "KALAN", "BİÇİM TARİHİ", "HASAT (KG)", "DÖNÜM/KG", "AÇIKLAMA"],
            "examples": [
                ["EKLE", "", "1", "Kuzey Tarla", "50", "100", "5000", "3000", "2000", "2024-06-15", "25000", "500", "İyi hasat"],
                ["GÜNCELLE", "1", "", "", "", "", "", "4000", "1000", "", "", "", "Ek tahsilat"],
                ["SİL", "2", "", "", "", "", "", "", "", "", "", "", ""],
                ["", "", "", "", "", "", "", "", "", "", "", "", ""],
                ["AÇIKLAMA:", "", "", "", "", "", "", "", "", "", "", "", ""],
                ["", "Dönüm: Tarla büyüklüğü", "", "", "", "", "", "", "", "", "", "", ""],
                ["", "Biçme fiyatı: Dönüm başına fiyat", "", "", "", "", "", "", "", "", "", "", ""],
                ["", "Toplam tutar = Dönüm × Biçme fiyatı", "", "", "", "", "", "", "", "", "", "", ""],
                ["", "Kalan = Toplam tutar - Tahsilat", "", "", "", "", "", "", "", "", "", "", ""]
            ]
        },
        "9-KANTAR FİŞİ": {
            "headers": ["İŞLEM TİPİ", "FİŞ ID", "TARLA ID", "MÜŞTERİ ID", "1. TARTIM", "2. TARTIM", "NET KG", "PLAKA", "ŞOFÖR ADI", "AÇIKLAMA"],
            "examples": [
                ["EKLE", "", "1", "1", "15000", "5000", "10000", "34ABC123", "Mehmet Şoför", "Normal tartım"],
                ["GÜNCELLE", "1", "", "", "15200", "5100", "10100", "", "", "Düzeltme"],
                ["SİL", "2", "", "", "", "", "", "", "", ""],
                ["", "", "", "", "", "", "", "", "", ""],
                ["AÇIKLAMA:", "", "", "", "", "", "", "", "", ""],
                ["", "1. Tartım: Dolu araç ağırlığı", "", "", "", "", "", "", "", ""],
                ["", "2. Tartım: Boş araç ağırlığı", "", "", "", "", "", "", "", ""],
                ["", "Net KG = 1. Tartım - 2. Tartım", "", "", "", "", "", "", "", ""],
                ["", "Tarla ID: Tarla Yönetimi sayfasından alın", "", "", "", "", "", "", "", ""]
            ]
        },
        "10-KULLANICILAR": {
            "headers": ["İŞLEM TİPİ", "KULLANICI ID", "KULLANICI ADI", "ŞİFRE", "AD SOYAD", "E-POSTA", "TELEFON", "ROL", "AKTİF", "AÇIKLAMA"],
            "examples": [
                ["EKLE", "", "admin2", "123456", "Ali Yönetici", "<EMAIL>", "05551234567", "Yönetici", "EVET", "Yeni yönetici"],
                ["GÜNCELLE", "1", "", "yenisifre", "", "", "", "", "", "Şifre değiştirildi"],
                ["SİL", "2", "", "", "", "", "", "", "", ""],
                ["", "", "", "", "", "", "", "", "", ""],
                ["AÇIKLAMA:", "", "", "", "", "", "", "", "", ""],
                ["", "Rol: Yönetici, Kullanıcı, Misafir", "", "", "", "", "", "", "", ""],
                ["", "Aktif: EVET/HAYIR", "", "", "", "", "", "", "", ""],
                ["", "Şifre güvenliği için dikkatli olun", "", "", "", "", "", "", "", ""]
            ]
        },
        "11-BİLDİRİMLER": {
            "headers": ["İŞLEM TİPİ", "BİLDİRİM ID", "BAŞLIK", "MESAJ", "TİP", "KULLANICI ID", "ÖNCELİK", "AKTİF", "AÇIKLAMA"],
            "examples": [
                ["EKLE", "", "Sistem Bakımı", "Yarın sistem bakımda olacak", "Bilgi", "1", "2", "EVET", "Genel duyuru"],
                ["GÜNCELLE", "1", "", "", "", "", "3", "", "Öncelik artırıldı"],
                ["SİL", "2", "", "", "", "", "", "", ""],
                ["", "", "", "", "", "", "", "", ""],
                ["AÇIKLAMA:", "", "", "", "", "", "", "", ""],
                ["", "Tip: Bilgi, Uyarı, Hata, Başarı, Sistem", "", "", "", "", "", "", ""],
                ["", "Öncelik: 1=Düşük, 2=Normal, 3=Yüksek, 4=Kritik", "", "", "", "", "", "", ""],
                ["", "Aktif: EVET/HAYIR", "", "", "", "", "", "", ""]
            ]
        },
        "12-KULLANIM KILAVUZU": {
            "headers": ["AÇIKLAMA", "", "", ""],
            "examples": [
                ["KOBİPANEL EXCEL CRUD KULLANIM KILAVUZU", "", "", ""],
                ["", "", "", ""],
                ["GENEL KULLANIM:", "", "", ""],
                ["", "", "", ""],
                ["1. İŞLEM TİPLERİ:", "", "", ""],
                ["   • EKLE: Yeni kayıt eklemek için", "", "", ""],
                ["   • GÜNCELLE: Mevcut kaydı güncellemek için (ID gerekli)", "", "", ""],
                ["   • SİL: Kaydı silmek için (sadece ID gerekli)", "", "", ""],
                ["", "", "", ""],
                ["2. GENEL KURALLAR:", "", "", ""],
                ["   • ID alanları güncelleme ve silme için zorunludur", "", "", ""],
                ["   • Tarih formatı: YYYY-MM-DD (örn: 2024-01-15)", "", "", ""],
                ["   • EVET/HAYIR alanları: sadece EVET veya HAYIR yazın", "", "", ""],
                ["   • Para tutarları: virgül veya nokta kullanabilirsiniz", "", "", ""],
                ["   • Boş bırakılan alanlar güncellenmez", "", "", ""],
                ["", "", "", ""],
                ["3. SAYFA AÇIKLAMALARI:", "", "", ""],
                ["   • 1-MÜŞTERİLER: Müşteri bilgileri", "", "", ""],
                ["   • 2-ÜRÜNLER: Ürün kataloğu", "", "", ""],
                ["   • 3-SATIŞLAR: Satış işlemleri", "", "", ""],
                ["   • 4-GİDERLER: Gider kayıtları", "", "", ""],
                ["   • 5-NOTLAR: Not ve hatırlatıcılar", "", "", ""],
                ["   • 6-ARAÇLAR: Araç bilgileri", "", "", ""],
                ["   • 7-YAKIT TAKİBİ: Yakıt alım kayıtları", "", "", ""],
                ["   • 8-TARLA YÖNETİMİ: Tarla ve hasat bilgileri", "", "", ""],
                ["   • 9-KANTAR FİŞİ: Tartım kayıtları", "", "", ""],
                ["   • 10-KULLANICILAR: Sistem kullanıcıları", "", "", ""],
                ["   • 11-BİLDİRİMLER: Sistem bildirimleri", "", "", ""],
                ["", "", "", ""],
                ["4. ÖNEMLİ NOTLAR:", "", "", ""],
                ["   • Her sayfada örnek veriler bulunur", "", "", ""],
                ["   • Örnekleri inceleyerek format öğrenin", "", "", ""],
                ["   • ID'leri diğer sayfalardan kontrol edin", "", "", ""],
                ["   • Veri girişi yapmadan önce örnekleri silin", "", "", ""],
                ["   • Büyük/küçük harf duyarlılığı yoktur", "", "", ""],
                ["", "", "", ""],
                ["5. HATA DURUMUNDA:", "", "", ""],
                ["   • Format hatalarını kontrol edin", "", "", ""],
                ["   • Zorunlu alanların dolu olduğundan emin olun", "", "", ""],
                ["   • ID'lerin doğru olduğunu kontrol edin", "", "", ""],
                ["   • Tarih formatını kontrol edin", "", "", ""],
                ["", "", "", ""],
                ["6. İLETİŞİM:", "", "", ""],
                ["   • Teknik destek için sistem yöneticisine başvurun", "", "", ""],
                ["   • Bu dosyayı düzenli olarak yedekleyin", "", "", ""],
                ["   • Önemli verileri kaybetmemek için dikkatli olun", "", "", ""]
            ]
        }
    }
    
    # Her sayfa için ayrı CSV dosyası oluştur
    for sheet_name, data in sheets_data.items():
        filename = f"{sheet_name}.csv"
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)
            
            # Başlık satırı
            writer.writerow([f"{sheet_name} - CRUD İŞLEMLERİ"])
            writer.writerow([])  # Boş satır
            
            # Sütun başlıkları
            writer.writerow(data["headers"])
            
            # Örnek veriler
            writer.writerows(data["examples"])
        
        print(f"Oluşturuldu: {filename}")
    
    print("\nTüm CSV dosyaları oluşturuldu!")
    print("Bu dosyaları Excel'de açabilir ve .xlsx formatında kaydedebilirsiniz.")
    print("\nExcel'de açmak için:")
    print("1. Excel'i açın")
    print("2. Dosya > Aç > CSV dosyasını seçin")
    print("3. UTF-8 kodlamasını seçin")
    print("4. Dosya > Farklı Kaydet > Excel Çalışma Kitabı (.xlsx) formatında kaydedin")
    
    return True

if __name__ == "__main__":
    create_excel_with_csv_data()
