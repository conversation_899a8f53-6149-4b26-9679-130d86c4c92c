#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
KobiPanel CRUD İşlemleri Excel Dosyası Oluşturucu
Bu script, KobiPanel projesindeki tüm modeller için CRUD işlemlerini
çok tecrübesiz kullanıcılar için kolay kullanılabilir Excel formatında oluşturur.
"""

try:
    from openpyxl import Workbook
    from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
    from openpyxl.utils import get_column_letter
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    print("openpyxl kütüphanesi bulunamadı. CSV formatında dosyalar oluşturulacak.")

import csv
from datetime import datetime

def create_excel_file():
    if OPENPYXL_AVAILABLE:
        return create_with_openpyxl()
    else:
        return create_with_csv()

def create_with_openpyxl():
    # Yeni Excel dosyası oluştur
    wb = Workbook()
    
    # Varsayılan sheet'i sil
    wb.remove(wb.active)
    
    # Stil tanımlamaları
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    center_alignment = Alignment(horizontal="center", vertical="center")
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # 1. MÜŞTERILER SAYFASI
    ws_musteriler = wb.create_sheet("1-MÜŞTERİLER")
    create_musteriler_sheet(ws_musteriler, header_font, header_fill, center_alignment, border)
    
    # 2. ÜRÜNLER SAYFASI
    ws_urunler = wb.create_sheet("2-ÜRÜNLER")
    create_urunler_sheet(ws_urunler, header_font, header_fill, center_alignment, border)
    
    # 3. SATIŞLAR SAYFASI
    ws_satislar = wb.create_sheet("3-SATIŞLAR")
    create_satislar_sheet(ws_satislar, header_font, header_fill, center_alignment, border)
    
    # 4. GİDERLER SAYFASI
    ws_giderler = wb.create_sheet("4-GİDERLER")
    create_giderler_sheet(ws_giderler, header_font, header_fill, center_alignment, border)
    
    # 5. NOTLAR SAYFASI
    ws_notlar = wb.create_sheet("5-NOTLAR")
    create_notlar_sheet(ws_notlar, header_font, header_fill, center_alignment, border)
    
    # 6. ARAÇLAR SAYFASI
    ws_araclar = wb.create_sheet("6-ARAÇLAR")
    create_araclar_sheet(ws_araclar, header_font, header_fill, center_alignment, border)
    
    # 7. YAKIT TAKİBİ SAYFASI
    ws_yakit = wb.create_sheet("7-YAKIT TAKİBİ")
    create_yakit_sheet(ws_yakit, header_font, header_fill, center_alignment, border)
    
    # 8. TARLA YÖNETİMİ SAYFASI
    ws_tarla = wb.create_sheet("8-TARLA YÖNETİMİ")
    create_tarla_sheet(ws_tarla, header_font, header_fill, center_alignment, border)
    
    # 9. KANTAR FİŞİ SAYFASI
    ws_kantar = wb.create_sheet("9-KANTAR FİŞİ")
    create_kantar_sheet(ws_kantar, header_font, header_fill, center_alignment, border)
    
    # 10. KULLANICILAR SAYFASI
    ws_kullanicilar = wb.create_sheet("10-KULLANICILAR")
    create_kullanicilar_sheet(ws_kullanicilar, header_font, header_fill, center_alignment, border)
    
    # 11. BİLDİRİMLER SAYFASI
    ws_bildirimler = wb.create_sheet("11-BİLDİRİMLER")
    create_bildirimler_sheet(ws_bildirimler, header_font, header_fill, center_alignment, border)
    
    # 12. KULLANIM KILAVUZU SAYFASI
    ws_kilavuz = wb.create_sheet("12-KULLANIM KILAVUZU")
    create_kilavuz_sheet(ws_kilavuz, header_font, header_fill, center_alignment, border)
    
    # Dosyayı kaydet
    wb.save("KobiPanel_CRUD_Islemleri.xlsx")
    print("Excel dosyası başarıyla oluşturuldu: KobiPanel_CRUD_Islemleri.xlsx")
    return True

def create_musteriler_sheet(ws, header_font, header_fill, center_alignment, border):
    """Müşteriler sayfasını oluşturur"""
    ws.title = "1-MÜŞTERİLER"
    
    # Başlık
    ws.merge_cells('A1:L1')
    ws['A1'] = "MÜŞTERİ YÖNETİMİ - CRUD İŞLEMLERİ"
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = center_alignment
    ws['A1'].fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
    
    # Açıklama
    ws.merge_cells('A2:L2')
    ws['A2'] = "Bu sayfada müşteri bilgilerini ekleyebilir, güncelleyebilir ve silebilirsiniz."
    ws['A2'].font = Font(italic=True)
    ws['A2'].alignment = center_alignment
    
    # Boş satır
    ws.append([])
    
    # Sütun başlıkları
    headers = [
        "İŞLEM TİPİ", "ID", "AD SOYAD", "TELEFON", "YAŞ", 
        "İL", "İLÇE", "MAHALLE", "KÜÇÜKBAŞ HAYVAN", "BÜYÜKBAŞ HAYVAN",
        "KAYIT TARİHİ", "AÇIKLAMA"
    ]
    
    ws.append(headers)
    
    # Header stilini uygula
    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=4, column=col)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = border
    
    # Örnek veriler ve açıklamalar
    examples = [
        ["EKLE", "", "Ahmet Yılmaz", "05551234567", "45", "İstanbul", "Kadıköy", "Fenerbahçe", "50", "20", "2024-01-15", "Yeni müşteri"],
        ["GÜNCELLE", "1", "Mehmet Demir", "05559876543", "38", "Ankara", "Çankaya", "Kızılay", "30", "15", "", "Telefon güncellendi"],
        ["SİL", "2", "", "", "", "", "", "", "", "", "", "Müşteriyi sil"],
        ["", "", "", "", "", "", "", "", "", "", "", ""],
        ["AÇIKLAMA:", "", "", "", "", "", "", "", "", "", "", ""],
        ["", "EKLE: Yeni müşteri eklemek için", "", "", "", "", "", "", "", "", "", ""],
        ["", "GÜNCELLE: Mevcut müşteriyi güncellemek için ID gerekli", "", "", "", "", "", "", "", "", "", ""],
        ["", "SİL: Müşteriyi silmek için sadece ID yeterli", "", "", "", "", "", "", "", "", "", ""],
        ["", "ID: Güncelleme ve silme işlemleri için gerekli", "", "", "", "", "", "", "", "", "", ""],
        ["", "Tarih formatı: YYYY-MM-DD (örn: 2024-01-15)", "", "", "", "", "", "", "", "", "", ""]
    ]
    
    for example in examples:
        ws.append(example)
    
    # Sütun genişliklerini ayarla
    column_widths = [12, 8, 20, 15, 8, 12, 12, 15, 15, 15, 15, 25]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width

def create_urunler_sheet(ws, header_font, header_fill, center_alignment, border):
    """Ürünler sayfasını oluşturur"""
    ws.title = "2-ÜRÜNLER"
    
    # Başlık
    ws.merge_cells('A1:H1')
    ws['A1'] = "ÜRÜN YÖNETİMİ - CRUD İŞLEMLERİ"
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = center_alignment
    ws['A1'].fill = PatternFill(start_color="E2EFDA", end_color="E2EFDA", fill_type="solid")
    
    # Açıklama
    ws.merge_cells('A2:H2')
    ws['A2'] = "Bu sayfada ürün bilgilerini yönetebilirsiniz."
    ws['A2'].font = Font(italic=True)
    ws['A2'].alignment = center_alignment
    
    ws.append([])
    
    # Sütun başlıkları
    headers = ["İŞLEM TİPİ", "ÜRÜN ID", "ÜRÜN ADI", "FİYAT (TL)", "STOK", "NAKLİYE ÜCRETİ", "EKLENME TARİHİ", "AÇIKLAMA"]
    ws.append(headers)
    
    # Header stilini uygula
    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=4, column=col)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = border
    
    # Örnek veriler
    examples = [
        ["EKLE", "", "Buğday", "15.50", "1000", "2.00", "2024-01-15", "Kaliteli buğday"],
        ["GÜNCELLE", "1", "Arpa", "12.75", "500", "1.50", "", "Fiyat güncellendi"],
        ["SİL", "2", "", "", "", "", "", "Ürünü sil"],
        ["", "", "", "", "", "", "", ""],
        ["AÇIKLAMA:", "", "", "", "", "", "", ""],
        ["", "Fiyatlar virgülle yazılabilir (örn: 15,50)", "", "", "", "", "", ""],
        ["", "Stok sayısal değer olmalı", "", "", "", "", "", ""],
        ["", "Nakliye ücreti opsiyonel", "", "", "", "", "", ""]
    ]
    
    for example in examples:
        ws.append(example)
    
    # Sütun genişliklerini ayarla
    column_widths = [12, 10, 20, 12, 10, 15, 15, 25]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width

def create_satislar_sheet(ws, header_font, header_fill, center_alignment, border):
    """Satışlar sayfasını oluşturur"""
    ws.title = "3-SATIŞLAR"

    # Başlık
    ws.merge_cells('A1:M1')
    ws['A1'] = "SATIŞ YÖNETİMİ - CRUD İŞLEMLERİ"
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = center_alignment
    ws['A1'].fill = PatternFill(start_color="FFF2CC", end_color="FFF2CC", fill_type="solid")

    # Açıklama
    ws.merge_cells('A2:M2')
    ws['A2'] = "Bu sayfada satış işlemlerini yönetebilirsiniz."
    ws['A2'].font = Font(italic=True)
    ws['A2'].alignment = center_alignment

    ws.append([])

    # Sütun başlıkları
    headers = [
        "İŞLEM TİPİ", "SATIŞ ID", "MÜŞTERİ ID", "ÜRÜN ID", "ADET",
        "TUTAR", "İNDİRİM", "SERVİS", "KREDİ KARTI", "NAKLİYE",
        "TESLİM DURUMU", "SATIŞ TARİHİ", "AÇIKLAMA"
    ]
    ws.append(headers)

    # Header stilini uygula
    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=4, column=col)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = border

    # Örnek veriler
    examples = [
        ["EKLE", "", "1", "1", "100", "1550.00", "50.00", "EVET", "HAYIR", "200", "HAYIR", "2024-01-15", "Toplu satış"],
        ["GÜNCELLE", "1", "", "", "", "", "", "", "", "", "EVET", "", "Teslim edildi"],
        ["SİL", "2", "", "", "", "", "", "", "", "", "", "", "Satışı iptal et"],
        ["", "", "", "", "", "", "", "", "", "", "", "", ""],
        ["AÇIKLAMA:", "", "", "", "", "", "", "", "", "", "", "", ""],
        ["", "Servis: EVET/HAYIR (Adrese teslim mi?)", "", "", "", "", "", "", "", "", "", "", ""],
        ["", "Kredi Kartı: EVET/HAYIR", "", "", "", "", "", "", "", "", "", "", ""],
        ["", "Teslim Durumu: EVET/HAYIR", "", "", "", "", "", "", "", "", "", "", ""],
        ["", "Müşteri ID ve Ürün ID'leri diğer sayfalardan alın", "", "", "", "", "", "", "", "", "", "", ""]
    ]

    for example in examples:
        ws.append(example)

    # Sütun genişliklerini ayarla
    column_widths = [12, 10, 12, 10, 8, 12, 10, 10, 12, 10, 15, 15, 25]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width

def create_giderler_sheet(ws, header_font, header_fill, center_alignment, border):
    """Giderler sayfasını oluşturur"""
    ws.title = "4-GİDERLER"

    # Başlık
    ws.merge_cells('A1:E1')
    ws['A1'] = "GİDER YÖNETİMİ - CRUD İŞLEMLERİ"
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = center_alignment
    ws['A1'].fill = PatternFill(start_color="FCE4D6", end_color="FCE4D6", fill_type="solid")

    # Açıklama
    ws.merge_cells('A2:E2')
    ws['A2'] = "Bu sayfada gider kayıtlarını yönetebilirsiniz."
    ws['A2'].font = Font(italic=True)
    ws['A2'].alignment = center_alignment

    ws.append([])

    # Sütun başlıkları
    headers = ["İŞLEM TİPİ", "GİDER ID", "KONU", "TUTAR (TL)", "TARİH"]
    ws.append(headers)

    # Header stilini uygula
    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=4, column=col)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = border

    # Örnek veriler
    examples = [
        ["EKLE", "", "Yakıt", "500.00", "2024-01-15"],
        ["EKLE", "", "Elektrik Faturası", "250.75", "2024-01-15"],
        ["GÜNCELLE", "1", "Yakıt (Güncellenmiş)", "550.00", ""],
        ["SİL", "2", "", "", ""],
        ["", "", "", "", ""],
        ["AÇIKLAMA:", "", "", "", ""],
        ["", "Konu: Giderin ne olduğunu açıklayın", "", "", ""],
        ["", "Tutar: Virgülle yazabilirsiniz (örn: 250,75)", "", "", ""],
        ["", "Tarih: YYYY-MM-DD formatında", "", "", ""]
    ]

    for example in examples:
        ws.append(example)

    # Sütun genişliklerini ayarla
    column_widths = [12, 10, 25, 15, 15]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width

def create_notlar_sheet(ws, header_font, header_fill, center_alignment, border):
    """Notlar sayfasını oluşturur"""
    ws.title = "5-NOTLAR"

    # Başlık
    ws.merge_cells('A1:E1')
    ws['A1'] = "NOT YÖNETİMİ - CRUD İŞLEMLERİ"
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = center_alignment
    ws['A1'].fill = PatternFill(start_color="EDEDED", end_color="EDEDED", fill_type="solid")

    # Açıklama
    ws.merge_cells('A2:E2')
    ws['A2'] = "Bu sayfada not ve hatırlatıcılarınızı yönetebilirsiniz."
    ws['A2'].font = Font(italic=True)
    ws['A2'].alignment = center_alignment

    ws.append([])

    # Sütun başlıkları
    headers = ["İŞLEM TİPİ", "NOT ID", "KONU", "AÇIKLAMA", "TARİH"]
    ws.append(headers)

    # Header stilini uygula
    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=4, column=col)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = border

    # Örnek veriler
    examples = [
        ["EKLE", "", "Toplantı", "Yarın saat 14:00'te müşteri toplantısı", "2024-01-15"],
        ["EKLE", "", "Hatırlatma", "Ay sonunda faturaları kontrol et", "2024-01-15"],
        ["GÜNCELLE", "1", "Toplantı İptal", "Toplantı ertelendi", ""],
        ["SİL", "2", "", "", ""],
        ["", "", "", "", ""],
        ["AÇIKLAMA:", "", "", "", ""],
        ["", "Konu: Notun başlığı", "", "", ""],
        ["", "Açıklama: Detaylı bilgi", "", "", ""],
        ["", "Tarih otomatik eklenir", "", "", ""]
    ]

    for example in examples:
        ws.append(example)

    # Sütun genişliklerini ayarla
    column_widths = [12, 10, 20, 40, 15]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width

def create_araclar_sheet(ws, header_font, header_fill, center_alignment, border):
    """Araçlar sayfasını oluşturur"""
    ws.title = "6-ARAÇLAR"

    # Başlık
    ws.merge_cells('A1:K1')
    ws['A1'] = "ARAÇ YÖNETİMİ - CRUD İŞLEMLERİ"
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = center_alignment
    ws['A1'].fill = PatternFill(start_color="D5E8D4", end_color="D5E8D4", fill_type="solid")

    # Açıklama
    ws.merge_cells('A2:K2')
    ws['A2'] = "Bu sayfada araç bilgilerini yönetebilirsiniz."
    ws['A2'].font = Font(italic=True)
    ws['A2'].alignment = center_alignment

    ws.append([])

    # Sütun başlıkları
    headers = [
        "İŞLEM TİPİ", "ARAÇ ID", "PLAKA", "MARKA", "MODEL",
        "MODEL YILI", "VİZE BİTİŞ", "SİGORTA BİTİŞ", "EGZOZ BİTİŞ",
        "TESCİL NO", "AÇIKLAMA"
    ]
    ws.append(headers)

    # Header stilini uygula
    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=4, column=col)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = border

    # Örnek veriler
    examples = [
        ["EKLE", "", "34ABC123", "Ford", "Transit", "2020", "2024-12-31", "2024-06-30", "2024-03-15", "12345678", "İş aracı"],
        ["GÜNCELLE", "1", "", "", "", "", "2025-12-31", "", "", "", "Vize yenilendi"],
        ["SİL", "2", "", "", "", "", "", "", "", "", ""],
        ["", "", "", "", "", "", "", "", "", "", ""],
        ["AÇIKLAMA:", "", "", "", "", "", "", "", "", "", ""],
        ["", "Plaka: 34ABC123 formatında", "", "", "", "", "", "", "", "", ""],
        ["", "Tarihler: YYYY-MM-DD formatında", "", "", "", "", "", "", "", "", ""],
        ["", "Model yılı: Sadece yıl (örn: 2020)", "", "", "", "", "", "", "", "", ""]
    ]

    for example in examples:
        ws.append(example)

    # Sütun genişliklerini ayarla
    column_widths = [12, 10, 12, 12, 12, 12, 12, 12, 12, 15, 25]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width

def create_yakit_sheet(ws, header_font, header_fill, center_alignment, border):
    """Yakıt takibi sayfasını oluşturur"""
    ws.title = "7-YAKIT TAKİBİ"

    # Başlık
    ws.merge_cells('A1:J1')
    ws['A1'] = "YAKIT TAKİBİ - CRUD İŞLEMLERİ"
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = center_alignment
    ws['A1'].fill = PatternFill(start_color="FFE6CC", end_color="FFE6CC", fill_type="solid")

    # Açıklama
    ws.merge_cells('A2:J2')
    ws['A2'] = "Bu sayfada araçların yakıt alımlarını takip edebilirsiniz."
    ws['A2'].font = Font(italic=True)
    ws['A2'].alignment = center_alignment

    ws.append([])

    # Sütun başlıkları
    headers = [
        "İŞLEM TİPİ", "YAKIT ID", "ARAÇ ID", "TUTAR (TL)", "LİTRE",
        "ALINDIĞI YER", "KM", "TARİH", "LİTRE FİYATI", "AÇIKLAMA"
    ]
    ws.append(headers)

    # Header stilini uygula
    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=4, column=col)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = border

    # Örnek veriler
    examples = [
        ["EKLE", "", "1", "500.00", "25.5", "Shell Benzinlik", "125000", "2024-01-15", "19.60", "Tam depo"],
        ["GÜNCELLE", "1", "", "520.00", "", "", "", "", "", "Tutar düzeltildi"],
        ["SİL", "2", "", "", "", "", "", "", "", ""],
        ["", "", "", "", "", "", "", "", "", ""],
        ["AÇIKLAMA:", "", "", "", "", "", "", "", "", ""],
        ["", "Araç ID: Araçlar sayfasından alın", "", "", "", "", "", "", "", ""],
        ["", "Litre fiyatı otomatik hesaplanabilir", "", "", "", "", "", "", "", ""],
        ["", "KM: Yakıt alındığı andaki kilometre", "", "", "", "", "", "", "", ""]
    ]

    for example in examples:
        ws.append(example)

    # Sütun genişliklerini ayarla
    column_widths = [12, 10, 10, 12, 10, 20, 10, 12, 12, 25]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width

def create_tarla_sheet(ws, header_font, header_fill, center_alignment, border):
    """Tarla yönetimi sayfasını oluşturur"""
    ws.title = "8-TARLA YÖNETİMİ"

    # Başlık
    ws.merge_cells('A1:M1')
    ws['A1'] = "TARLA YÖNETİMİ - CRUD İŞLEMLERİ"
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = center_alignment
    ws['A1'].fill = PatternFill(start_color="E1D5E7", end_color="E1D5E7", fill_type="solid")

    # Açıklama
    ws.merge_cells('A2:M2')
    ws['A2'] = "Bu sayfada biçilen tarlaları ve hasat bilgilerini yönetebilirsiniz."
    ws['A2'].font = Font(italic=True)
    ws['A2'].alignment = center_alignment

    ws.append([])

    # Sütun başlıkları
    headers = [
        "İŞLEM TİPİ", "TARLA ID", "MÜŞTERİ ID", "TARLA ADI", "DÖNÜM",
        "BİÇME FİYATI", "TOPLAM TUTAR", "TAHSİLAT", "KALAN",
        "BİÇİM TARİHİ", "HASAT (KG)", "DÖNÜM/KG", "AÇIKLAMA"
    ]
    ws.append(headers)

    # Header stilini uygula
    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=4, column=col)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = border

    # Örnek veriler
    examples = [
        ["EKLE", "", "1", "Kuzey Tarla", "50", "100", "5000", "3000", "2000", "2024-06-15", "25000", "500", "İyi hasat"],
        ["GÜNCELLE", "1", "", "", "", "", "", "4000", "1000", "", "", "", "Ek tahsilat"],
        ["SİL", "2", "", "", "", "", "", "", "", "", "", "", ""],
        ["", "", "", "", "", "", "", "", "", "", "", "", ""],
        ["AÇIKLAMA:", "", "", "", "", "", "", "", "", "", "", "", ""],
        ["", "Dönüm: Tarla büyüklüğü", "", "", "", "", "", "", "", "", "", "", ""],
        ["", "Biçme fiyatı: Dönüm başına fiyat", "", "", "", "", "", "", "", "", "", "", ""],
        ["", "Toplam tutar = Dönüm × Biçme fiyatı", "", "", "", "", "", "", "", "", "", "", ""],
        ["", "Kalan = Toplam tutar - Tahsilat", "", "", "", "", "", "", "", "", "", "", ""]
    ]

    for example in examples:
        ws.append(example)

    # Sütun genişliklerini ayarla
    column_widths = [12, 10, 12, 15, 10, 12, 12, 12, 10, 12, 12, 10, 25]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width

def create_kantar_sheet(ws, header_font, header_fill, center_alignment, border):
    """Kantar fişi sayfasını oluşturur"""
    ws.title = "9-KANTAR FİŞİ"

    # Başlık
    ws.merge_cells('A1:J1')
    ws['A1'] = "KANTAR FİŞİ YÖNETİMİ - CRUD İŞLEMLERİ"
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = center_alignment
    ws['A1'].fill = PatternFill(start_color="F8CECC", end_color="F8CECC", fill_type="solid")

    # Açıklama
    ws.merge_cells('A2:J2')
    ws['A2'] = "Bu sayfada kantar fişlerini ve tartım bilgilerini yönetebilirsiniz."
    ws['A2'].font = Font(italic=True)
    ws['A2'].alignment = center_alignment

    ws.append([])

    # Sütun başlıkları
    headers = [
        "İŞLEM TİPİ", "FİŞ ID", "TARLA ID", "MÜŞTERİ ID", "1. TARTIM",
        "2. TARTIM", "NET KG", "PLAKA", "ŞOFÖR ADI", "AÇIKLAMA"
    ]
    ws.append(headers)

    # Header stilini uygula
    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=4, column=col)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = border

    # Örnek veriler
    examples = [
        ["EKLE", "", "1", "1", "15000", "5000", "10000", "34ABC123", "Mehmet Şoför", "Normal tartım"],
        ["GÜNCELLE", "1", "", "", "15200", "5100", "10100", "", "", "Düzeltme"],
        ["SİL", "2", "", "", "", "", "", "", "", ""],
        ["", "", "", "", "", "", "", "", "", ""],
        ["AÇIKLAMA:", "", "", "", "", "", "", "", "", ""],
        ["", "1. Tartım: Dolu araç ağırlığı", "", "", "", "", "", "", "", ""],
        ["", "2. Tartım: Boş araç ağırlığı", "", "", "", "", "", "", "", ""],
        ["", "Net KG = 1. Tartım - 2. Tartım", "", "", "", "", "", "", "", ""],
        ["", "Tarla ID: Tarla Yönetimi sayfasından alın", "", "", "", "", "", "", "", ""]
    ]

    for example in examples:
        ws.append(example)

    # Sütun genişliklerini ayarla
    column_widths = [12, 10, 10, 12, 12, 12, 12, 12, 15, 25]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width

def create_kullanicilar_sheet(ws, header_font, header_fill, center_alignment, border):
    """Kullanıcılar sayfasını oluşturur"""
    ws.title = "10-KULLANICILAR"

    # Başlık
    ws.merge_cells('A1:J1')
    ws['A1'] = "KULLANICI YÖNETİMİ - CRUD İŞLEMLERİ"
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = center_alignment
    ws['A1'].fill = PatternFill(start_color="DAE8FC", end_color="DAE8FC", fill_type="solid")

    # Açıklama
    ws.merge_cells('A2:J2')
    ws['A2'] = "Bu sayfada sistem kullanıcılarını yönetebilirsiniz."
    ws['A2'].font = Font(italic=True)
    ws['A2'].alignment = center_alignment

    ws.append([])

    # Sütun başlıkları
    headers = [
        "İŞLEM TİPİ", "KULLANICI ID", "KULLANICI ADI", "ŞİFRE", "AD SOYAD",
        "E-POSTA", "TELEFON", "ROL", "AKTİF", "AÇIKLAMA"
    ]
    ws.append(headers)

    # Header stilini uygula
    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=4, column=col)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = border

    # Örnek veriler
    examples = [
        ["EKLE", "", "admin2", "123456", "Ali Yönetici", "<EMAIL>", "05551234567", "Yönetici", "EVET", "Yeni yönetici"],
        ["GÜNCELLE", "1", "", "yenisifre", "", "", "", "", "", "Şifre değiştirildi"],
        ["SİL", "2", "", "", "", "", "", "", "", ""],
        ["", "", "", "", "", "", "", "", "", ""],
        ["AÇIKLAMA:", "", "", "", "", "", "", "", "", ""],
        ["", "Rol: Yönetici, Kullanıcı, Misafir", "", "", "", "", "", "", "", ""],
        ["", "Aktif: EVET/HAYIR", "", "", "", "", "", "", "", ""],
        ["", "Şifre güvenliği için dikkatli olun", "", "", "", "", "", "", "", ""]
    ]

    for example in examples:
        ws.append(example)

    # Sütun genişliklerini ayarla
    column_widths = [12, 12, 15, 12, 20, 25, 15, 12, 10, 25]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width

def create_bildirimler_sheet(ws, header_font, header_fill, center_alignment, border):
    """Bildirimler sayfasını oluşturur"""
    ws.title = "11-BİLDİRİMLER"

    # Başlık
    ws.merge_cells('A1:I1')
    ws['A1'] = "BİLDİRİM YÖNETİMİ - CRUD İŞLEMLERİ"
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = center_alignment
    ws['A1'].fill = PatternFill(start_color="FFF2CC", end_color="FFF2CC", fill_type="solid")

    # Açıklama
    ws.merge_cells('A2:I2')
    ws['A2'] = "Bu sayfada sistem bildirimlerini yönetebilirsiniz."
    ws['A2'].font = Font(italic=True)
    ws['A2'].alignment = center_alignment

    ws.append([])

    # Sütun başlıkları
    headers = [
        "İŞLEM TİPİ", "BİLDİRİM ID", "BAŞLIK", "MESAJ", "TİP",
        "KULLANICI ID", "ÖNCELİK", "AKTİF", "AÇIKLAMA"
    ]
    ws.append(headers)

    # Header stilini uygula
    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=4, column=col)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = border

    # Örnek veriler
    examples = [
        ["EKLE", "", "Sistem Bakımı", "Yarın sistem bakımda olacak", "Bilgi", "1", "2", "EVET", "Genel duyuru"],
        ["GÜNCELLE", "1", "", "", "", "", "3", "", "Öncelik artırıldı"],
        ["SİL", "2", "", "", "", "", "", "", ""],
        ["", "", "", "", "", "", "", "", ""],
        ["AÇIKLAMA:", "", "", "", "", "", "", "", ""],
        ["", "Tip: Bilgi, Uyarı, Hata, Başarı, Sistem", "", "", "", "", "", "", ""],
        ["", "Öncelik: 1=Düşük, 2=Normal, 3=Yüksek, 4=Kritik", "", "", "", "", "", "", ""],
        ["", "Aktif: EVET/HAYIR", "", "", "", "", "", "", ""]
    ]

    for example in examples:
        ws.append(example)

    # Sütun genişliklerini ayarla
    column_widths = [12, 12, 20, 30, 12, 12, 10, 10, 25]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width

def create_kantar_sheet(ws, header_font, header_fill, center_alignment, border):
    """Kantar fişi sayfasını oluşturur"""
    ws.title = "9-KANTAR FİŞİ"

    # Başlık
    ws.merge_cells('A1:J1')
    ws['A1'] = "KANTAR FİŞİ YÖNETİMİ - CRUD İŞLEMLERİ"
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = center_alignment
    ws['A1'].fill = PatternFill(start_color="F8CECC", end_color="F8CECC", fill_type="solid")

    # Açıklama
    ws.merge_cells('A2:J2')
    ws['A2'] = "Bu sayfada kantar fişlerini ve tartım bilgilerini yönetebilirsiniz."
    ws['A2'].font = Font(italic=True)
    ws['A2'].alignment = center_alignment

    ws.append([])

    # Sütun başlıkları
    headers = [
        "İŞLEM TİPİ", "FİŞ ID", "TARLA ID", "MÜŞTERİ ID", "1. TARTIM",
        "2. TARTIM", "NET KG", "PLAKA", "ŞOFÖR ADI", "AÇIKLAMA"
    ]
    ws.append(headers)

    # Header stilini uygula
    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=4, column=col)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = border

    # Örnek veriler
    examples = [
        ["EKLE", "", "1", "1", "15000", "5000", "10000", "34ABC123", "Mehmet Şoför", "Normal tartım"],
        ["GÜNCELLE", "1", "", "", "15200", "5100", "10100", "", "", "Düzeltme"],
        ["SİL", "2", "", "", "", "", "", "", "", ""],
        ["", "", "", "", "", "", "", "", "", ""],
        ["AÇIKLAMA:", "", "", "", "", "", "", "", "", ""],
        ["", "1. Tartım: Dolu araç ağırlığı", "", "", "", "", "", "", "", ""],
        ["", "2. Tartım: Boş araç ağırlığı", "", "", "", "", "", "", "", ""],
        ["", "Net KG = 1. Tartım - 2. Tartım", "", "", "", "", "", "", "", ""],
        ["", "Tarla ID: Tarla Yönetimi sayfasından alın", "", "", "", "", "", "", "", ""]
    ]

    for example in examples:
        ws.append(example)

    # Sütun genişliklerini ayarla
    column_widths = [12, 10, 10, 12, 12, 12, 12, 12, 15, 25]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width

def create_kullanicilar_sheet(ws, header_font, header_fill, center_alignment, border):
    """Kullanıcılar sayfasını oluşturur"""
    ws.title = "10-KULLANICILAR"

    # Başlık
    ws.merge_cells('A1:J1')
    ws['A1'] = "KULLANICI YÖNETİMİ - CRUD İŞLEMLERİ"
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = center_alignment
    ws['A1'].fill = PatternFill(start_color="DAE8FC", end_color="DAE8FC", fill_type="solid")

    # Açıklama
    ws.merge_cells('A2:J2')
    ws['A2'] = "Bu sayfada sistem kullanıcılarını yönetebilirsiniz."
    ws['A2'].font = Font(italic=True)
    ws['A2'].alignment = center_alignment

    ws.append([])

    # Sütun başlıkları
    headers = [
        "İŞLEM TİPİ", "KULLANICI ID", "KULLANICI ADI", "ŞİFRE", "AD SOYAD",
        "E-POSTA", "TELEFON", "ROL", "AKTİF", "AÇIKLAMA"
    ]
    ws.append(headers)

    # Header stilini uygula
    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=4, column=col)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = border

    # Örnek veriler
    examples = [
        ["EKLE", "", "admin2", "123456", "Ali Yönetici", "<EMAIL>", "05551234567", "Yönetici", "EVET", "Yeni yönetici"],
        ["GÜNCELLE", "1", "", "yenisifre", "", "", "", "", "", "Şifre değiştirildi"],
        ["SİL", "2", "", "", "", "", "", "", "", ""],
        ["", "", "", "", "", "", "", "", "", ""],
        ["AÇIKLAMA:", "", "", "", "", "", "", "", "", ""],
        ["", "Rol: Yönetici, Kullanıcı, Misafir", "", "", "", "", "", "", "", ""],
        ["", "Aktif: EVET/HAYIR", "", "", "", "", "", "", "", ""],
        ["", "Şifre güvenliği için dikkatli olun", "", "", "", "", "", "", "", ""]
    ]

    for example in examples:
        ws.append(example)

    # Sütun genişliklerini ayarla
    column_widths = [12, 12, 15, 12, 20, 25, 15, 12, 10, 25]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width

def create_bildirimler_sheet(ws, header_font, header_fill, center_alignment, border):
    """Bildirimler sayfasını oluşturur"""
    ws.title = "11-BİLDİRİMLER"

    # Başlık
    ws.merge_cells('A1:I1')
    ws['A1'] = "BİLDİRİM YÖNETİMİ - CRUD İŞLEMLERİ"
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = center_alignment
    ws['A1'].fill = PatternFill(start_color="FFF2CC", end_color="FFF2CC", fill_type="solid")

    # Açıklama
    ws.merge_cells('A2:I2')
    ws['A2'] = "Bu sayfada sistem bildirimlerini yönetebilirsiniz."
    ws['A2'].font = Font(italic=True)
    ws['A2'].alignment = center_alignment

    ws.append([])

    # Sütun başlıkları
    headers = [
        "İŞLEM TİPİ", "BİLDİRİM ID", "BAŞLIK", "MESAJ", "TİP",
        "KULLANICI ID", "ÖNCELİK", "AKTİF", "AÇIKLAMA"
    ]
    ws.append(headers)

    # Header stilini uygula
    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=4, column=col)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = border

    # Örnek veriler
    examples = [
        ["EKLE", "", "Sistem Bakımı", "Yarın sistem bakımda olacak", "Bilgi", "1", "2", "EVET", "Genel duyuru"],
        ["GÜNCELLE", "1", "", "", "", "", "3", "", "Öncelik artırıldı"],
        ["SİL", "2", "", "", "", "", "", "", ""],
        ["", "", "", "", "", "", "", "", ""],
        ["AÇIKLAMA:", "", "", "", "", "", "", "", ""],
        ["", "Tip: Bilgi, Uyarı, Hata, Başarı, Sistem", "", "", "", "", "", "", ""],
        ["", "Öncelik: 1=Düşük, 2=Normal, 3=Yüksek, 4=Kritik", "", "", "", "", "", "", ""],
        ["", "Aktif: EVET/HAYIR", "", "", "", "", "", "", ""]
    ]

    for example in examples:
        ws.append(example)

    # Sütun genişliklerini ayarla
    column_widths = [12, 12, 20, 30, 12, 12, 10, 10, 25]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width

def create_kilavuz_sheet(ws, header_font, header_fill, center_alignment, border):
    """Kullanım kılavuzu sayfasını oluşturur"""
    ws.title = "12-KULLANIM KILAVUZU"

    # Başlık
    ws.merge_cells('A1:D1')
    ws['A1'] = "KOBİPANEL EXCEL CRUD KULLANIM KILAVUZU"
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = center_alignment
    ws['A1'].fill = PatternFill(start_color="C5E0B4", end_color="C5E0B4", fill_type="solid")

    ws.append([])

    # Genel açıklamalar
    instructions = [
        ["GENEL KULLANIM:", "", "", ""],
        ["", "", "", ""],
        ["1. İŞLEM TİPLERİ:", "", "", ""],
        ["   • EKLE: Yeni kayıt eklemek için", "", "", ""],
        ["   • GÜNCELLE: Mevcut kaydı güncellemek için (ID gerekli)", "", "", ""],
        ["   • SİL: Kaydı silmek için (sadece ID gerekli)", "", "", ""],
        ["", "", "", ""],
        ["2. GENEL KURALLAR:", "", "", ""],
        ["   • ID alanları güncelleme ve silme için zorunludur", "", "", ""],
        ["   • Tarih formatı: YYYY-MM-DD (örn: 2024-01-15)", "", "", ""],
        ["   • EVET/HAYIR alanları: sadece EVET veya HAYIR yazın", "", "", ""],
        ["   • Para tutarları: virgül veya nokta kullanabilirsiniz", "", "", ""],
        ["   • Boş bırakılan alanlar güncellenmez", "", "", ""],
        ["", "", "", ""],
        ["3. SAYFA AÇIKLAMALARI:", "", "", ""],
        ["   • 1-MÜŞTERİLER: Müşteri bilgileri", "", "", ""],
        ["   • 2-ÜRÜNLER: Ürün kataloğu", "", "", ""],
        ["   • 3-SATIŞLAR: Satış işlemleri", "", "", ""],
        ["   • 4-GİDERLER: Gider kayıtları", "", "", ""],
        ["   • 5-NOTLAR: Not ve hatırlatıcılar", "", "", ""],
        ["   • 6-ARAÇLAR: Araç bilgileri", "", "", ""],
        ["   • 7-YAKIT TAKİBİ: Yakıt alım kayıtları", "", "", ""],
        ["   • 8-TARLA YÖNETİMİ: Tarla ve hasat bilgileri", "", "", ""],
        ["   • 9-KANTAR FİŞİ: Tartım kayıtları", "", "", ""],
        ["   • 10-KULLANICILAR: Sistem kullanıcıları", "", "", ""],
        ["   • 11-BİLDİRİMLER: Sistem bildirimleri", "", "", ""],
        ["", "", "", ""],
        ["4. ÖNEMLİ NOTLAR:", "", "", ""],
        ["   • Her sayfada örnek veriler bulunur", "", "", ""],
        ["   • Örnekleri inceleyerek format öğrenin", "", "", ""],
        ["   • ID'leri diğer sayfalardan kontrol edin", "", "", ""],
        ["   • Veri girişi yapmadan önce örnekleri silin", "", "", ""],
        ["   • Büyük/küçük harf duyarlılığı yoktur", "", "", ""],
        ["", "", "", ""],
        ["5. HATA DURUMUNDA:", "", "", ""],
        ["   • Format hatalarını kontrol edin", "", "", ""],
        ["   • Zorunlu alanların dolu olduğundan emin olun", "", "", ""],
        ["   • ID'lerin doğru olduğunu kontrol edin", "", "", ""],
        ["   • Tarih formatını kontrol edin", "", "", ""],
        ["", "", "", ""],
        ["6. İLETİŞİM:", "", "", ""],
        ["   • Teknik destek için sistem yöneticisine başvurun", "", "", ""],
        ["   • Bu dosyayı düzenli olarak yedekleyin", "", "", ""],
        ["   • Önemli verileri kaybetmemek için dikkatli olun", "", "", ""]
    ]

    for instruction in instructions:
        ws.append(instruction)

    # Sütun genişliklerini ayarla
    ws.column_dimensions['A'].width = 40
    ws.column_dimensions['B'].width = 20
    ws.column_dimensions['C'].width = 20
    ws.column_dimensions['D'].width = 20

def create_with_csv():
    """CSV formatında dosyalar oluşturur (openpyxl yoksa)"""
    print("CSV formatında dosyalar oluşturuluyor...")

    # Her sayfa için ayrı CSV dosyası oluştur
    sheets_data = {
        "1-Musteriler": [
            ["İŞLEM TİPİ", "ID", "AD SOYAD", "TELEFON", "YAŞ", "İL", "İLÇE", "MAHALLE", "KÜÇÜKBAŞ", "BÜYÜKBAŞ", "AÇIKLAMA"],
            ["EKLE", "", "Ahmet Yılmaz", "05551234567", "45", "İstanbul", "Kadıköy", "Fenerbahçe", "50", "20", "Yeni müşteri"],
            ["GÜNCELLE", "1", "Mehmet Demir", "05559876543", "38", "Ankara", "Çankaya", "Kızılay", "30", "15", "Telefon güncellendi"],
            ["SİL", "2", "", "", "", "", "", "", "", "", "Müşteriyi sil"]
        ],
        "2-Urunler": [
            ["İŞLEM TİPİ", "ÜRÜN ID", "ÜRÜN ADI", "FİYAT", "STOK", "NAKLİYE", "AÇIKLAMA"],
            ["EKLE", "", "Buğday", "15.50", "1000", "2.00", "Kaliteli buğday"],
            ["GÜNCELLE", "1", "Arpa", "12.75", "500", "1.50", "Fiyat güncellendi"],
            ["SİL", "2", "", "", "", "", "Ürünü sil"]
        ]
        # Diğer sayfalar da eklenebilir...
    }

    for sheet_name, data in sheets_data.items():
        filename = f"{sheet_name}.csv"
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerows(data)
        print(f"Oluşturuldu: {filename}")

    print("CSV dosyaları oluşturuldu. Excel'de açabilirsiniz.")
    return True

if __name__ == "__main__":
    create_excel_file()
