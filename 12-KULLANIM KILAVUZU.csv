﻿12-KULLANIM KILAVUZU - CRUD İŞLEMLERİ

AÇIKLAMA,,,
KOBİPANEL EXCEL CRUD KULLANIM KILAVUZU,,,
,,,
GENEL KULLANIM:,,,
,,,
1. İŞLEM TİPLERİ:,,,
   • EKLE: Yeni kayıt eklemek için,,,
   • GÜNCELLE: Mevcut kaydı güncellemek için (ID gerekli),,,
   • SİL: <PERSON><PERSON><PERSON> (sadece ID gerekli),,,
,,,
2. GENEL KURALLAR:,,,
   • ID alanları güncelleme ve silme için zorunludur,,,
   • Tarih formatı: YYYY-MM-DD (örn: 2024-01-15),,,
   • EVET/HAYIR alanları: sadece EVET veya HAYIR yazın,,,
   • Para tutarları: virgül veya nokta kullanabilirsiniz,,,
   • <PERSON>ş bırakılan alanlar güncellenmez,,,
,,,
3. SAYFA AÇIKLAMALARI:,,,
   • 1-MÜŞTERİLER: Müşteri bilgileri,,,
   • 2-ÜRÜNLER: Ürün kataloğu,,,
   • 3-SATIŞLAR: Satış işlemleri,,,
   • 4-GİDERLER: Gider kayıtları,,,
   • 5-NOTLAR: Not ve hatırlatıcılar,,,
   • 6-ARAÇLAR: Araç bilgileri,,,
   • 7-YAKIT TAKİBİ: Yakıt alım kayıtları,,,
   • 8-TARLA YÖNETİMİ: Tarla ve hasat bilgileri,,,
   • 9-KANTAR FİŞİ: Tartım kayıtları,,,
   • 10-KULLANICILAR: Sistem kullanıcıları,,,
   • 11-BİLDİRİMLER: Sistem bildirimleri,,,
,,,
4. ÖNEMLİ NOTLAR:,,,
   • Her sayfada örnek veriler bulunur,,,
   • Örnekleri inceleyerek format öğrenin,,,
   • ID'leri diğer sayfalardan kontrol edin,,,
   • Veri girişi yapmadan önce örnekleri silin,,,
   • Büyük/küçük harf duyarlılığı yoktur,,,
,,,
5. HATA DURUMUNDA:,,,
   • Format hatalarını kontrol edin,,,
   • Zorunlu alanların dolu olduğundan emin olun,,,
   • ID'lerin doğru olduğunu kontrol edin,,,
   • Tarih formatını kontrol edin,,,
,,,
6. İLETİŞİM:,,,
   • Teknik destek için sistem yöneticisine başvurun,,,
   • Bu dosyayı düzenli olarak yedekleyin,,,
   • Önemli verileri kaybetmemek için dikkatli olun,,,
